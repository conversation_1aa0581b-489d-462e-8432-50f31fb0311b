
import 'package:flutter/material.dart';

import '../../../domain/entities/customer_get_room_list.dart';
import '../../../domain/entities/tag_result_image.dart';
import '../../widgets/widgets.dart';

class TagByResultImageListBody extends StatefulWidget {
  const TagByResultImageListBody({super.key, required this.tags, required this.rooms});
  final List<String> tags;
  final List<CustomerGetRoomListItem> rooms;
  @override
  State<TagByResultImageListBody> createState() 
  => _TagByResultImageListBodyState();
}

class _TagByResultImageListBodyState extends State<TagByResultImageListBody> {
    final ValueNotifierList<TagResultImageListItems?> 
    images = ValueNotifierList(
    [],
  );
  TagResultImageList? data;
  @override
  void initState() {
    super.initState();
    images.setValue([
       TagResultImageListItems(avatar: "https://mediadev.theadvance.com/theadvance/d50f356d-29df-42fc-b0af-f299b1a1f2ba"),
        TagResultImageListItems(avatar: "https://mediadev.theadvance.com/theadvance/d50f356d-29df-42fc-b0af-f299b1a1f2ba"),
         TagResultImageListItems(avatar: "https://mediadev.theadvance.com/theadvance/d50f356d-29df-42fc-b0af-f299b1a1f2ba"),
          TagResultImageListItems(avatar: "https://mediadev.theadvance.com/theadvance/d50f356d-29df-42fc-b0af-f299b1a1f2ba"),
       TagResultImageListItems(avatar: "https://mediadev.theadvance.com/theadvance/d50f356d-29df-42fc-b0af-f299b1a1f2ba"),
      TagResultImageListItems(avatar: "https://mediadev.theadvance.com/theadvance/d50f356d-29df-42fc-b0af-f299b1a1f2ba")
    ]);
  }
  @override
  Widget build(final BuildContext context) {
    return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: Column(
            spacing: 12,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
            TabBar(
              
              tabs: tabs),
            SizedBox(
              child: Wrap(

                children: [
                  ...List.generate(widget.tags.length,
                   (i){
                    final tag = widget.tags[i];
                    return Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child:  SizedBox(
                    child: Row(
                      children: [
                        Text(tag),
                        IconButton(onPressed: (){
                          
                        }, icon: Icon(Icons.close))
                      ],
                    ),
                  ),
                    );
                   })
                 
                ],
              ),
            ),
            Expanded(
              child: ValueListenableBuilder(
                    valueListenable: images,
                    builder: (final context, final vServices, final child) {
                      final buildServiceWidgets = vServices
                          .map(
                            (final e) => ColoredBox(color: Colors.amber)
                          )
                          .toList();
                      return GridView.count(
                        crossAxisCount: 3,
                        shrinkWrap: true,
                        physics: const BouncingScrollPhysics(),
                        mainAxisSpacing: 16,
                        crossAxisSpacing: 16,
                        childAspectRatio: 0.9,
                        children: buildServiceWidgets,
                      );
                    },
                  ),
            ),
            ],
          ),
        );
  }
}
