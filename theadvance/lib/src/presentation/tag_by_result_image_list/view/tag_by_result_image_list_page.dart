import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';

import '../../../domain/entities/customer_get_room_list.dart';
import '../../product_confirm/widgets/base_layout.dart';
import '../widget/tag_by_result_image_list_body.dart';

@RoutePage()
class TagByResultImageListPage extends StatelessWidget {
  const TagByResultImageListPage({super.key, required this.tags, required this.rooms});
  final List<CustomerGetRoomListItem> rooms;
  final List<String> tags;
  @override
  Widget build(final BuildContext context) {
    return BaseLayout(
      body:  TagByResultImageListBody(tags: tags, rooms: rooms,),
      title: Text(
        'Danh sách ảnh',
        style: Theme.of(context).textTheme.labelLarge?.copyWith(
          fontWeight: FontWeight.w500,
          fontSize: 20,
        ),
      ),
    );
  }
}
