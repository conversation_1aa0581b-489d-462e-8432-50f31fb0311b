
import 'package:auto_route/auto_route.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';
import 'package:flutter/material.dart';

import '../../../core/routes/routes.dart';
import '../../../data/datasources/local/cache/hive/ez_cache.dart';
import '../../product_confirm/widgets/base_layout.dart';
import '../widget/tag_image_search_body.dart';

@RoutePage()
class TagImageSearchPage extends StatefulWidget {
  const TagImageSearchPage({super.key});

  @override
  State<TagImageSearchPage> createState() => _TagImageSearchPageState();
}

class _TagImageSearchPageState extends State<TagImageSearchPage> {
  late TextEditingController controller;
  @override
  void initState() {
    controller = TextEditingController(text: '');
    super.initState();
  }
  @override
  Widget build(final BuildContext context) {
    return BaseLayout(
      title: Text(
          context.l10n.picture,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w500,
            fontSize: 20,
          ),
        ),
      bottomAppBarChild:  Padding(
              padding: const EdgeInsets.all(12.0),
              child: Theme(
                data: ThemeData(
                  inputDecorationTheme: InputDecorationTheme(
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide.none,
                    ),
                  ),
                ),
                child: TextField(
                  controller: controller,
                  autofocus: true,
                  onSubmitted: (final val){
                    
                    //  EZCache.shared.saveStickerRecent(
                    //       histories
                    //           .map((final e) => e.toJson())
                    //           .toList(),
                    //     );
                  },
                  decoration: InputDecoration(
                    suffixIconConstraints: const BoxConstraints(
                      minWidth: 24,
                      minHeight: 24,
                    ),
                    prefixIconConstraints: const BoxConstraints(
                      minWidth: 24,
                      minHeight: 24,
                    ),
                    prefixIcon: Padding(
                      padding: const EdgeInsets.only(left: 12),
                      child: EZResources.image(
                        ImageParams(
                          name: AppIcons.icSearch,
                          color: Theme.of(context).hintColor,
                        ),
                      ),
                    ),
                    contentPadding: const EdgeInsets.only(
                      left: 12,
                      top: 22,
                      right: 22,
                    ),
                    hint: Text(
                      context.l10n.search,
                      style: TextStyle(color: Theme.of(context).hintColor),
                    ),
                    isDense: true,
                    filled: true,
                    fillColor: Colors.white,
                  ),
                ),
              ),
            ),
      body: const TagImageSearchBody(),
    );
  }
}


