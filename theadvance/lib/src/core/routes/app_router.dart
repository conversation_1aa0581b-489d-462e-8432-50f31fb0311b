// Dart imports:
import 'dart:io';

// Flutter imports:
import 'package:flutter/material.dart';

// Package imports:
import 'package:auto_route/auto_route.dart';
import 'package:ez_login_authentication/ez_login_authentication.dart';
import 'package:stringee_flutter_plugin/stringee_flutter_plugin.dart';
import 'package:video_player/video_player.dart';

// Project imports:
import '../../acc/pages/asset_history/views/asset_history_screen.dart';
import '../../acc/pages/asset_history/views/input_asset_code_screen.dart';
import '../../acc/pages/home/<USER>/view/home_screen.dart';
import '../../acc/pages/input_inventory_barcode/ui/view/input_inventory_barcode_screen.dart';
import '../../acc/pages/inventory_form/ui/view/inventory_ticket_form_page.dart';
import '../../acc/pages/inventory_ticket/ui/view/inventory_ticket_screen.dart';
import '../../acc/pages/list_inventory/ui/view/choose_agency_page.dart';
import '../../acc/pages/list_inventory/ui/view/list_inventory.dart';
import '../../acc/pages/login/view/login_page.dart';
import '../../acc/pages/splash/landing_page.dart';
import '../../acc/pages/splash/splash.dart';
import '../../acc/pages/tabbar/ui/tab_bar.dart';
import '../../config/enums/app_enum.dart';
import '../../config/enums/medical_department_code.dart';
import '../../data/models/nd_models.dart';
import '../../domain/entities/consultation_service.dart';
import '../../domain/entities/customer_get_room_list.dart';
import '../../domain/entities/entities.dart';
import '../../presentation/account/view/account_asset_page.dart';
import '../../presentation/account/view/account_preview_page.dart';
import '../../presentation/action_attendance/view/action_attendance_page.dart';
import '../../presentation/assign_task/assign_task.dart';
import '../../presentation/bed_selection/bed_selection.dart';
import '../../presentation/branch_chat_list/branch_chat_list.dart';
import '../../presentation/branch_selection/branch_selection.dart';
import '../../presentation/calling/view/calling_page.dart';
import '../../presentation/chat/chat.dart';
import '../../presentation/chat_list/view/chat_list_page.dart';
import '../../presentation/chat_select_branch/chat_select_branch.dart';
import '../../presentation/checkin_photo/checkin_photo.dart';
import '../../presentation/collaborator/camera_checkin/view/camera_preview_page.dart';
import '../../presentation/collaborator/checkin_reminder/view/checkin_reminder_page.dart';
import '../../presentation/collaborator/collaborator.dart';
import '../../presentation/collaborator/home_find/view/custom_image_review_page.dart';
import '../../presentation/collaborator/home_find/view/home_find_page.dart';
import '../../presentation/collaborator/more/more.dart';
import '../../presentation/collaborator/order_food/view/order_food_complaint_page.dart';
import '../../presentation/collaborator/order_food/view/order_food_page.dart';
import '../../presentation/collaborator/order_food/view/order_food_regulations_page.dart';
import '../../presentation/collaborator/profile/view/profile_gallery_page.dart';
import '../../presentation/collaborator/profile/view/profile_preview_image_page.dart';
import '../../presentation/comment_list/view/comment_list_update_page.dart';
import '../../presentation/consultation_customer/view/consultation_customer_page.dart';
import '../../presentation/consultation_history/consultation_history.dart';
import '../../presentation/consultation_history_detail/consultation_history_detail.dart';
import '../../presentation/consultation_manager/consultation_manager.dart';
import '../../presentation/create_chat_folder/create_chat_folder.dart';
import '../../presentation/create_chat_group/view/create_chat_group_page.dart';
import '../../presentation/create_customer/view/create_customer_page.dart';
import '../../presentation/customer_booking_info/customer_booking_info.dart';
import '../../presentation/customer_info_details/customer_info_details.dart';
import '../../presentation/customer_list/customer_list.dart';
import '../../presentation/customer_profile/customer_profile.dart';
import '../../presentation/customer_record/customer_record.dart';
import '../../presentation/customer_schedule/customer_schedule.dart';
import '../../presentation/detail_crm_customer/detail_crm_customer.dart';
import '../../presentation/detail_staff_evaluation_period/detail_staff_evaluation_period.dart';
import '../../presentation/dev/dev.dart';
import '../../presentation/event_presentation/event_login/event_login.dart';
import '../../presentation/event_presentation/event_presentation.dart';
import '../../presentation/event_presentation/main/checkout/checkout.dart';
import '../../presentation/event_presentation/main/reward/reward.dart';
import '../../presentation/group_chat_detail/view/group_chat_detail_page.dart';
import '../../presentation/hr_organization/view/hr_organization_page.dart';
import '../../presentation/important_notes/important_notes.dart';
import '../../presentation/kpi_employee/view/kpi_employee_detail_page.dart';
import '../../presentation/kpi_employee/view/kpi_employee_page.dart';
import '../../presentation/list_customer/list_customer.dart';
import '../../presentation/media/view/media_page.dart';
import '../../presentation/medical_department_list/medical_department_list.dart';
import '../../presentation/medical_log_detail/medical_log_detail.dart';
import '../../presentation/medical_product_creation/medical_product_creation.dart';
import '../../presentation/medical_service_creation/medical_service_creation.dart';
import '../../presentation/medical_service_list/medical_service_list.dart';
import '../../presentation/medical_service_log_list/medical_service_log_list.dart';
import '../../presentation/medical_template_list/medical_template_list.dart';
import '../../presentation/medicine_detail/medicine_detail.dart';
import '../../presentation/note_details/note_details.dart';
import '../../presentation/notification_list/view/notification_list_page.dart';
import '../../presentation/product_confirm/view/product_confirm_page.dart';
import '../../presentation/product_confirm/view/product_detail_confirm_page.dart';
import '../../presentation/px_list/px_list.dart';
import '../../presentation/px_task_list/px_task_list.dart';
import '../../presentation/rating_human/view/rating_human_page.dart';
import '../../presentation/schedule_details/view/schedule_details_page.dart';
import '../../presentation/select_px_room/select_px_room.dart';
import '../../presentation/settings/views/fonts_settings_screen.dart';
import '../../presentation/settings/views/settings_screen.dart';
import '../../presentation/staff_evaluation_periods/staff_evaluation_periods.dart';
import '../../presentation/story_detail/view/story_detail_page.dart';
import '../../presentation/story_list/view/story_edit_image_page.dart';
import '../../presentation/story_list/view/story_image_detail_page.dart';
import '../../presentation/story_list/view/story_search_page.dart';
import '../../presentation/story_list/view/story_video_detail_page.dart';
import '../../presentation/story_list/view/story_video_fullscreen_page.dart';
import '../../presentation/story_list/view/story_write_page.dart';
import '../../presentation/story_list/widgets/story_video.dart';
import '../../presentation/story_person_list/view/story_person_list_page.dart';
import '../../presentation/taking_care_customer/taking_care_customer.dart';
import '../../presentation/ticket/view/ticket_page.dart';
import '../../presentation/ticket_detail/view/ticket_detail_other_page.dart';
import '../../presentation/ticket_detail/view/ticket_detail_page.dart';
import '../../presentation/user_list/view/user_list_page.dart';
import '../../presentation/user_ticket/view/user_ticket_page.dart';
import '../../presentation/tag_image/view/tag_image_page.dart';
import '../../presentation/tag_image_search/view/tag_image_search_page.dart';
import '../../presentation/tag_by_result_image_list/view/tag_by_result_image_list_page.dart';
import '../../presentation/widgets/widgets.dart';
import '../params/bed_selection_page_params.dart';
import '../params/customer_info_details_request_params.dart';
import '../params/story_write_info_request_params.dart';
import '../webview/nd_webview.dart';
import 'app_custom_route.dart';
import 'app_router_guard.dart';
import 'routes.dart';

//product_confirm_page

part 'app_router.gr.dart';

@AutoRouterConfig(replaceInRouteName: 'Page|Screen,Route')
class AppRouter extends RootStackRouter {
  @override
  RouteType get defaultRouteType =>
      const RouteType.custom(customRouteBuilder: appCustomRoute);
  @override
  final List<AutoRoute> routes = [
    CustomRoute(
      path: Routes.webView,
      page: WebViewRoute.page,
      transitionsBuilder: TransitionsBuilders.fadeIn,
      durationInMilliseconds: 400,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: '${Routes.notificationDetails}/:id',
      page: DetailNotificationRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: '${Routes.newsDetails}/:id',
      page: DetailNewsRoute.page,
    ),
    AutoRoute(path: '${Routes.confirmOTP}/:id', page: ConfirmOtpRoute.page),
    AutoRoute(path: Routes.setPassword, page: SetPasswordRoute.page),
    CustomRoute(
      path: Routes.biometrics,
      transitionsBuilder: TransitionsBuilders.fadeIn,
      page: BiometricsRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: '${Routes.jobSchedulerDetails}/:id',
      page: DetailJobSchedulerRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.eformDetails,
      page: DetailEformRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.selectedAssignee,
      page: SelectedAssigneeRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.selectingOffice,
      page: SelectingOfficeRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.selectingStaff,
      page: SelectingStaffRoute.page,
    ),
    AutoRoute(path: Routes.approvalOtp, page: ApprovalOtpRoute.page),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.creatingEform,
      page: CreatingEformRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: '${Routes.functionRoom}/:id',
      page: FunctionRoomRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: '${Routes.customer}/:id',
      page: CustomerRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: '${Routes.infoCustomer}/:id',
      page: InfoCustomerRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: '${Routes.monthlyHistoryCheckin}/:month/:year',
      page: MonthlyHistoryCheckinRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.eventLogin,
      page: EventLoginRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: '${Routes.eventConfirmOTP}/:id',
      page: EventConfirmOTPRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.eventActions,
      page: EventActionsRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.eventCheckIn,
      page: EventCheckInRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.eventUnmapPage,
      page: EventUnmapRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.eventCheckOut,
      page: EventCheckOutRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.eventReward,
      page: EventRewardRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.eventHistory,
      page: EventHistoryRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.baseWebview,
      page: BaseWebviewRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.accHomePage,
      page: AccHomeRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.accLogin,
      page: AccLoginRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.inventoryTicket,
      page: InventoryTicketRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.inputInventoryBarcode,
      page: InputInventoryBarCodeRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.chooseAgencyPage,
      page: ChooseAgencyRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: '${Routes.assetHistory}/:id',
      page: AssetHistoryRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.settings,
      page: SettingsRoute.page,
    ),
    AutoRoute(guards: [AuthGuard()], path: Routes.dev, page: DevRoute.page),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.fontsSettings,
      page: FontsSettingsRoute.page,
    ),
    CustomRoute(
      path: Routes.noInternetConnection,
      page: NoInternetRoute.page,
      transitionsBuilder: TransitionsBuilders.fadeIn,
      durationInMilliseconds: 400,
    ),
    CustomRoute(
      path: Routes.vpnConnected,
      page: VPNConnectedRoute.page,
      transitionsBuilder: TransitionsBuilders.fadeIn,
      durationInMilliseconds: 400,
    ),
    AutoRoute(path: Routes.landing, page: LandingRoute.page, initial: true),
    AutoRoute(path: Routes.onBoarding, page: OnBoardingRoute.page),
    AutoRoute(path: Routes.loginOptions, page: OptionLoginRoute.page),
    CustomRoute(
      path: Routes.homePage,
      page: MainRoute.page,
      transitionsBuilder: TransitionsBuilders.fadeIn,
      guards: [AuthGuard()],
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.notifications,
      page: NotificationsRoute.page,
    ),
    AutoRoute(guards: [AuthGuard()], path: Routes.news, page: NewsRoute.page),
    CustomRoute(
      path: Routes.login,
      page: LoginRoute.page,
      transitionsBuilder: TransitionsBuilders.fadeIn,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.eventEvents,
      page: NDEventsRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.profile,
      page: ProfileRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.creatingTask,
      page: CreatingTaskRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.language,
      page: LanguageRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.supportRequests,
      page: SupportRequestsRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.createSupportRequest,
      page: CreateSupportRequestsRoute.page,
    ),
    AutoRoute(guards: [AuthGuard()], path: Routes.eform, page: EformRoute.page),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.editHomeMenu,
      page: EditHomeMenuRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.checkinReminder,
      page: CheckinReminderRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.setting,
      page: SettingRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.inventory,
      page: ListInventoryRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.accLanding,
      page: AccLandingRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.eventSplash,
      page: EventSplashRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.inputAssetCode,
      page: InputAssetCodeRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.branchSelection,
      page: BranchSelectionRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.bedSelection,
      page: BedSelectionRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.importantNotes,
      page: ImportantNotesRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.customerProfile,
      page: CustomerProfileRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.consultationHistory,
      page: ConsultationHistoryRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.consultationHistoryDetail,
      page: ConsultationHistoryDetailRoute.page,
    ),
    AutoRoute(path: Routes.unknown, page: UnknownRouteRoute.page),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.listCustomer,
      page: ListCustomerRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.noteDetails,
      page: NoteDetailsRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.customerInfoDetails,
      page: CustomerInfoDetailsRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.medicalDepartmentList,
      page: MedicalDepartmentListRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.serviceAndProduct,
      page: ServiceAndProductRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.detailService,
      page: DetailServiceRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.medicalServiceList,
      page: MedicalServiceListRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.medicalServiceLogList,
      page: MedicalServiceLogListRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.medicalLogDetail,
      page: MedicalLogDetailRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.medicineDetail,
      page: MedicineDetailRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.medicalTemplateList,
      page: MedicalTemplateListRoute.page,
    ),
    CustomRoute(
      opaque: false,
      path: Routes.imagePreview,
      page: ImagePreviewRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.medicalProductCreation,
      page: MedicalProductCreationRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.medicalServiceCreation,
      page: MedicalServiceCreationRoute.page,
    ),
    CustomRoute(
      guards: [AuthGuard()],
      path: Routes.orderFood,
      page: OrderFoodRoute.page,
      transitionsBuilder: TransitionsBuilders.slideLeftWithFade,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.scheduleDetails,
      page: ScheduleDetailsRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.customerSchedule,
      page: CustomerScheduleRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.customerBookingInfo,
      page: CustomerBookingInfoRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.chatSelectBranch,
      page: ChatSelectBranchRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.assignTask,
      page: AssignTaskRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: '${Routes.branchChatList}/:branchId/:branchName',
      page: BranchChatListRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.pxList,
      page: PxListRoute.page,
      maintainState: false,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: '${Routes.pxTaskList}/:roomCode',
      page: PxTaskListRoute.page,
      maintainState: false,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: '${Routes.takingCareCustomer}/:assignId/:roomCode',
      page: TakingCareCustomerRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.createCustomer,
      page: CreateCustomerRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.createCustomer,
      page: CreateCustomerRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.selectPxRoom,
      page: SelectPxRoomRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.customerList,
      page: CustomerListRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.consultationManager,
      page: ConsultationManagerRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.consultationCustomer,
      page: ConsultationCustomerRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: '${Routes.staffEvaluationPeriods}/:organizationId',
      page: StaffEvaluationPeriodsRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.detailStaffEvaluationPeriod,
      page: DetailStaffEvaluationPeriodRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.userTicket,
      page: UserTicketRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.rating,
      page: RatingHumanRoute.page,
    ),
    AutoRoute(path: Routes.calling, page: CallingRoute.page),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.detailCrmCustomer,
      page: DetailCrmCustomerRoute.page,
    ),
    CustomRoute(
      guards: [AuthGuard()],
      path: '${Routes.hrOrganization}/:toNextPage',
      page: HrOrganizationRoute.page,
      transitionsBuilder: TransitionsBuilders.slideLeftWithFade,
    ),
    AutoRoute(guards: [AuthGuard()], path: Routes.media, page: MediaRoute.page),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.customerRecord,
      page: CustomerRecordRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.checkinPhoto,
      page: CheckinPhotoRoute.page,
    ),
    CustomRoute(
      path: Routes.chatList,
      page: ChatListRoute.page,
      transitionsBuilder: TransitionsBuilders.fadeIn,
      guards: [AuthGuard()],
    ),
    CustomRoute(
      path: '${Routes.chat}/:conversationId',
      page: ChatRoute.page,
      guards: [AuthGuard()],
      opaque: false,
      durationInMilliseconds: 200,
      reverseDurationInMilliseconds: 150,
      transitionsBuilder: TransitionsBuilders.slideLeft,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.checkin,
      page: HistoryCheckinRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.confirmCheckin,
      page: CameraPreviewRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.checkinAction,
      page: ActionAttendanceRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.accountPreview,
      page: AccountPreviewRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.accountAsset,
      page: AccountAssetRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.gallery,
      page: ProfileGalleryRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.previewAvatar,
      page: ProfilePreviewImageRoute.page,
    ),
    CustomRoute(
      path: Routes.createChatGroup,
      page: CreateChatGroupRoute.page,
      transitionsBuilder: TransitionsBuilders.slideBottom,
      guards: [AuthGuard()],
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.storyWrite,
      page: StoryWriteRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: '${Routes.storySearch}/:text',
      page: StorySearchRoute.page,
    ),
    CustomRoute(
      path: Routes.storyPerson,
      page: StoryPersonRoute.page,
      transitionsBuilder: TransitionsBuilders.fadeIn,
      guards: [AuthGuard()],
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.updateComment,
      page: CommentListUpdateRoute.page,
    ),
    CustomRoute(
      path: Routes.groupChatDetail,
      page: GroupChatDetailRoute.page,
      transitionsBuilder: TransitionsBuilders.fadeIn,
      guards: [AuthGuard()],
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.userList,
      page: UserListRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: '${Routes.storyDetail}/:id',
      page: StoryDetailRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.notificationList,
      page: NotificationListRoute.page,
    ),
    CustomRoute(
      path: Routes.storyVideoDetail,
      page: StoryVideoDetailRoute.page,
      opaque: false,
      guards: [AuthGuard()],
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.videoFullScreen,
      page: StoryVideoFullRoute.page,
    ),
    CustomRoute(
      path: Routes.storyImageDetailPage,
      page: StoryImageDetailRoute.page,
      opaque: false,
      transitionsBuilder: TransitionsBuilders.fadeIn,
      durationInMilliseconds: 450,
      reverseDurationInMilliseconds: 450,
      guards: [AuthGuard()],
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.createChatFolder,
      page: CreateChatFolderRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.ticket,
      page: TicketRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.orderFoodComplaint,
      page: OrderFoodComplaintRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: Routes.orderFoodRegulations,
      page: OrderFoodRegulationsRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: '${Routes.ticketDetail}/:id',
      page: TicketDetailRoute.page,
    ),
    AutoRoute(
      guards: [AuthGuard()],
      path: '${Routes.ticketDetailOther}/:id',
      page: TicketDetailOtherRoute.page,
    ),
    AutoRoute(path: Routes.storyEditImage, page: StoryEditImageRoute.page),
    CustomRoute(
      path: Routes.customerImage,
      page: HomeFindRoute.page,
      transitionsBuilder: TransitionsBuilders.slideLeftWithFade,
    ),
    CustomRoute(
      path: Routes.reviewCustomImage,
      page: CustomImageReviewRoute.page,
      transitionsBuilder: TransitionsBuilders.slideLeftWithFade,
    ),
    CustomRoute(
      path: Routes.kpiEmployee,
      page: KpiEmployeeRoute.page,
      transitionsBuilder: TransitionsBuilders.slideLeftWithFade,
    ),
    CustomRoute(
      path: Routes.kpiEmployeeDetail,
      page: KpiEmployeeDetailRoute.page,
      transitionsBuilder: TransitionsBuilders.slideLeftWithFade,
    ),
    CustomRoute(
      path: '${Routes.productConfirm}/:organizationId',
      page: ProductConfirmRoute.page,
      transitionsBuilder: TransitionsBuilders.slideLeftWithFade,
    ),
    CustomRoute(
      path: '${Routes.productDetailConfirm}/:organizationId/:id',
      page: ProductDetailConfirmRoute.page,
      transitionsBuilder: TransitionsBuilders.slideLeftWithFade,
    ),
    CustomRoute(
      path: Routes.tagImage,
      page: TagImageRoute.page,
      transitionsBuilder: TransitionsBuilders.slideLeftWithFade,
    ),
    CustomRoute(
      path: Routes.tagImageSearch,
      page: TagImageSearchRoute.page,
      transitionsBuilder: TransitionsBuilders.slideLeftWithFade,
    ),
    CustomRoute(
      path: Routes.tagImageResultSearch,
      page: TagByResultImageListRoute.page,
      transitionsBuilder: TransitionsBuilders.slideLeftWithFade,
    ),
    RedirectRoute(path: '*', redirectTo: Routes.unknown),
  ];
}
